/**
 * 测试编码修复脚本
 * 使用Node.js直接测试，避免PowerShell编码问题
 */

const http = require('http');

const config = {
  apiKey: 'xitool_9287d2e583dcfd3ecc137726435428f79548f8438174fec8ae9fa754a73c2ba8',
  boardId: '92aa739b-76e4-47c1-91cb-a9fee32fdaf5',
  columnId: '4b406d03-2ad1-4506-9cbe-937cd15d3bcc' // 待办列
};

async function sendMcpRequest(method, params = {}) {
  return new Promise((resolve, reject) => {
    const requestData = JSON.stringify({
      jsonrpc: '2.0',
      method: 'tools/call',
      params: {
        name: method,
        arguments: params
      },
      id: Date.now()
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/mcp-auth',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.setEncoding('utf8'); // 确保使用UTF-8解码
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(response);
        } catch (error) {
          resolve({ error: 'JSON解析失败', data });
        }
      });
    });

    req.on('error', reject);
    req.write(requestData);
    req.end();
  });
}

async function testEncodingFix() {
  console.log('🧪 开始测试编码修复...');
  
  try {
    // 1. 清空所有任务
    console.log('\n🧹 清空所有任务...');
    const clearResult = await sendMcpRequest('clear_all_tasks');
    if (clearResult.error) {
      console.log('❌ 清空任务失败:', clearResult.error.message);
      return;
    }
    console.log('✅ 任务清空成功');

    // 2. 创建测试任务（包含中文）
    console.log('\n➕ 创建包含中文的测试任务...');
    const testTask = {
      title: '🎯 中文编码测试任务',
      description: '这是一个测试中文UTF-8编码的任务。\n\n包含以下内容：\n- 中文字符\n- Emoji表情 🚀\n- 特殊符号 ©®™\n- 数字 123456\n- 英文 Hello World',
      priority: 'High',
      boardId: config.boardId,
      status: config.columnId
    };

    const createResult = await sendMcpRequest('submit_task_dataset', {
      tasks: [testTask]
    });

    if (createResult.error) {
      console.log('❌ 创建任务失败:', createResult.error.message);
      return;
    }
    console.log('✅ 任务创建成功');

    // 3. 验证任务内容
    console.log('\n🔍 验证任务内容...');
    const listResult = await sendMcpRequest('list_tasks');
    
    if (listResult.error) {
      console.log('❌ 获取任务列表失败:', listResult.error.message);
      return;
    }

    if (listResult.result && listResult.result.content && listResult.result.content[0]) {
      const tasks = JSON.parse(listResult.result.content[0].text);
      
      if (tasks.length > 0) {
        const task = tasks[0];
        console.log('📋 任务详情:');
        console.log(`   ID: ${task.id}`);
        console.log(`   标题: ${task.title}`);
        console.log(`   描述: ${task.description}`);
        console.log(`   优先级: ${task.priority}`);
        console.log(`   状态: ${task.status}`);
        console.log(`   看板ID: ${task.boardId}`);
        
        // 检查中文是否正确显示
        const hasChineseChars = /[\u4e00-\u9fff]/.test(task.title + task.description);
        const hasEmoji = /[\u{1f300}-\u{1f5ff}\u{1f900}-\u{1f9ff}\u{1f600}-\u{1f64f}\u{1f680}-\u{1f6ff}\u{2600}-\u{26ff}\u{2700}-\u{27bf}\u{1f1e6}-\u{1f1ff}\u{1f191}-\u{1f251}\u{1f004}\u{1f0cf}\u{1f170}-\u{1f171}\u{1f17e}-\u{1f17f}\u{1f18e}\u{3030}\u{2b50}\u{2b55}\u{2934}-\u{2935}\u{2b05}-\u{2b07}\u{2b1b}-\u{2b1c}\u{3297}\u{3299}\u{303d}\u{00a9}\u{00ae}\u{2122}\u{23f3}\u{24c2}\u{23e9}-\u{23ef}\u{25b6}\u{23f8}-\u{23fa}]/gu.test(task.title + task.description);
        
        console.log('\n🎯 编码检查结果:');
        console.log(`   包含中文字符: ${hasChineseChars ? '✅ 是' : '❌ 否'}`);
        console.log(`   包含Emoji: ${hasEmoji ? '✅ 是' : '❌ 否'}`);
        
        if (hasChineseChars && hasEmoji) {
          console.log('🎉 编码修复成功！中文和Emoji都能正确显示');
        } else {
          console.log('⚠️ 编码可能仍有问题，请检查前端显示');
        }
      } else {
        console.log('⚠️ 没有找到任务');
      }
    } else {
      console.log('❌ 无法解析任务列表响应');
    }

  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
testEncodingFix().then(() => {
  console.log('\n🎯 测试完成！');
  console.log('请检查前端界面 http://localhost:8080 查看任务是否正确显示');
}).catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
