/**
 * XItools MCP工具验证脚本
 * 
 * 此脚本将：
 * 1. 验证实际可用的MCP工具数量
 * 2. 测试每个工具的真实功能（不仅检查返回值）
 * 3. 验证数据是否真的被创建/修改/删除
 */

const http = require('http');

// 测试配置
const config = {
  baseUrl: 'http://localhost:3000',
  mcpEndpoint: '/mcp-auth',
  apiKey: 'xitool_71b0a8b4d0706d6b24bd5f26acb6cd7b794f45acce20dd0a9a73cff7a9e35527',
  timeout: 10000
};

/**
 * 发送JSON-RPC请求到MCP端点
 */
async function sendJsonRpcRequest(method, params = {}) {
  return new Promise((resolve, reject) => {
    const requestData = JSON.stringify({
      jsonrpc: '2.0',
      method: method,
      params: params,
      id: Date.now()
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: config.mcpEndpoint,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(requestData),
        'Authorization': `Bearer ${config.apiKey}`
      },
      timeout: config.timeout
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({
            status: res.statusCode,
            contentType: res.headers['content-type'],
            response: response,
            data: data
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            contentType: res.headers['content-type'],
            response: null,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.write(requestData);
    req.end();
  });
}

/**
 * 获取并验证所有MCP工具
 */
async function verifyAllTools() {
  console.log('🔍 验证XItools MCP工具...');
  console.log(`📡 测试端点: ${config.baseUrl}${config.mcpEndpoint}`);
  
  try {
    // 1. 获取工具列表
    console.log('\n📋 获取工具列表...');
    const toolsResult = await sendJsonRpcRequest('tools/list');
    
    if (toolsResult.status !== 200 || !toolsResult.response || !toolsResult.response.result) {
      console.log('❌ 无法获取工具列表');
      return;
    }
    
    const tools = toolsResult.response.result.tools;
    console.log(`✅ 发现 ${tools.length} 个MCP工具:`);
    
    // 按类别分组显示工具
    const toolsByCategory = {
      '任务管理': [],
      '看板列管理': [],
      '项目管理': [],
      '实用工具': []
    };
    
    tools.forEach((tool, index) => {
      console.log(`   ${index + 1}. ${tool.name} - ${tool.description}`);
      
      // 分类工具
      if (['submit_task_dataset', 'list_tasks', 'get_task_details', 'update_task', 'delete_task'].includes(tool.name)) {
        toolsByCategory['任务管理'].push(tool.name);
      } else if (['get_columns', 'create_column', 'update_column', 'delete_column', 'reorder_columns'].includes(tool.name)) {
        toolsByCategory['看板列管理'].push(tool.name);
      } else if (['get_workspaces', 'get_projects', 'get_boards'].includes(tool.name)) {
        toolsByCategory['项目管理'].push(tool.name);
      } else {
        toolsByCategory['实用工具'].push(tool.name);
      }
    });
    
    console.log('\n📊 工具分类统计:');
    Object.entries(toolsByCategory).forEach(([category, toolList]) => {
      console.log(`   ${category}: ${toolList.length}个 - ${toolList.join(', ')}`);
    });
    
    // 2. 验证README中声称的14个工具
    const expectedTools = [
      'submit_task_dataset', 'list_tasks', 'get_task_details', 'update_task', 'delete_task',
      'get_columns', 'create_column', 'update_column', 'delete_column', 'reorder_columns',
      'get_task_schema', 'clear_all_tasks', 'update_task_color',
      'get_workspaces', 'get_projects', 'get_boards' // 多看板管理工具
    ];
    
    console.log('\n🔍 验证README中声称的工具:');
    const actualToolNames = tools.map(t => t.name);
    const missingTools = expectedTools.filter(tool => !actualToolNames.includes(tool));
    const extraTools = actualToolNames.filter(tool => !expectedTools.includes(tool));
    
    if (missingTools.length > 0) {
      console.log(`❌ 缺失的工具 (${missingTools.length}个): ${missingTools.join(', ')}`);
    }
    
    if (extraTools.length > 0) {
      console.log(`➕ 额外的工具 (${extraTools.length}个): ${extraTools.join(', ')}`);
    }
    
    console.log(`📊 实际工具数量: ${tools.length} (README声称: 14个)`);
    
    return tools;
    
  } catch (error) {
    console.error('验证工具失败:', error.message);
    return [];
  }
}

/**
 * 深度功能验证 - 验证工具是否真的执行了操作
 */
async function deepFunctionalityTest() {
  console.log('\n🧪 开始深度功能验证...');
  
  try {
    // 1. 验证任务创建是否真的创建了数据
    console.log('\n📝 测试任务创建功能...');
    
    // 先获取当前任务数量
    const beforeTasks = await sendJsonRpcRequest('tools/call', {
      name: 'list_tasks',
      arguments: {}
    });
    
    let beforeCount = 0;
    if (beforeTasks.response && beforeTasks.response.result) {
      try {
        const tasks = JSON.parse(beforeTasks.response.result.content[0].text);
        beforeCount = tasks.length;
      } catch (e) {
        beforeCount = 0;
      }
    }
    
    console.log(`   创建前任务数量: ${beforeCount}`);
    
    // 获取看板信息
    const boardsResult = await sendJsonRpcRequest('tools/call', {
      name: 'get_boards',
      arguments: {}
    });
    
    if (!boardsResult.response || !boardsResult.response.result) {
      console.log('❌ 无法获取看板信息，跳过任务创建测试');
      return;
    }
    
    const boards = JSON.parse(boardsResult.response.result.content[0].text);
    if (boards.length === 0) {
      console.log('❌ 没有可用的看板，跳过任务创建测试');
      return;
    }
    
    const testBoard = boards[0];
    console.log(`   使用测试看板: ${testBoard.name} (${testBoard.id})`);
    
    // 创建测试任务
    const createResult = await sendJsonRpcRequest('tools/call', {
      name: 'submit_task_dataset',
      arguments: {
        tasks: [{
          title: `功能验证测试任务 ${Date.now()}`,
          description: '这是用于验证MCP工具真实功能的测试任务',
          priority: 'High',
          boardId: testBoard.id,
          status: 'todo'
        }]
      }
    });
    
    if (!createResult.response || createResult.response.error) {
      console.log('❌ 任务创建失败:', createResult.response?.error?.message || '未知错误');
      return;
    }
    
    console.log('✅ 任务创建请求成功');
    
    // 再次获取任务数量验证是否真的创建了
    const afterTasks = await sendJsonRpcRequest('tools/call', {
      name: 'list_tasks',
      arguments: {}
    });
    
    let afterCount = 0;
    let createdTask = null;
    if (afterTasks.response && afterTasks.response.result) {
      try {
        const tasks = JSON.parse(afterTasks.response.result.content[0].text);
        afterCount = tasks.length;
        // 找到刚创建的任务
        createdTask = tasks.find(t => t.title.includes('功能验证测试任务'));
      } catch (e) {
        afterCount = 0;
      }
    }
    
    console.log(`   创建后任务数量: ${afterCount}`);
    
    if (afterCount > beforeCount) {
      console.log('✅ 任务确实被创建了！数据库中任务数量增加');
      
      if (createdTask) {
        console.log(`   创建的任务ID: ${createdTask.id}`);
        console.log(`   任务标题: ${createdTask.title}`);
        
        // 测试任务更新功能
        console.log('\n✏️ 测试任务更新功能...');
        const updateResult = await sendJsonRpcRequest('tools/call', {
          name: 'update_task',
          arguments: {
            task_id: createdTask.id,
            updates: {
              title: createdTask.title + ' (已更新)',
              priority: 'Low'
            }
          }
        });
        
        if (updateResult.response && !updateResult.response.error) {
          console.log('✅ 任务更新请求成功');
          
          // 验证更新是否生效
          const detailResult = await sendJsonRpcRequest('tools/call', {
            name: 'get_task_details',
            arguments: {
              task_id: createdTask.id
            }
          });
          
          if (detailResult.response && detailResult.response.result) {
            const updatedTask = JSON.parse(detailResult.response.result.content[0].text);
            if (updatedTask.title.includes('(已更新)') && updatedTask.priority === 'Low') {
              console.log('✅ 任务确实被更新了！数据库中的数据已改变');
            } else {
              console.log('❌ 任务更新可能没有生效');
            }
          }
        } else {
          console.log('❌ 任务更新失败:', updateResult.response?.error?.message || '未知错误');
        }
        
        // 测试任务删除功能
        console.log('\n🗑️ 测试任务删除功能...');
        const deleteResult = await sendJsonRpcRequest('tools/call', {
          name: 'delete_task',
          arguments: {
            task_id: createdTask.id
          }
        });
        
        if (deleteResult.response && !deleteResult.response.error) {
          console.log('✅ 任务删除请求成功');
          
          // 验证删除是否生效
          const finalTasks = await sendJsonRpcRequest('tools/call', {
            name: 'list_tasks',
            arguments: {}
          });
          
          let finalCount = 0;
          if (finalTasks.response && finalTasks.response.result) {
            try {
              const tasks = JSON.parse(finalTasks.response.result.content[0].text);
              finalCount = tasks.length;
              const stillExists = tasks.find(t => t.id === createdTask.id);
              
              if (!stillExists && finalCount === beforeCount) {
                console.log('✅ 任务确实被删除了！数据库中的记录已清除');
              } else {
                console.log('❌ 任务删除可能没有生效');
              }
            } catch (e) {
              console.log('⚠️ 无法验证删除结果');
            }
          }
        } else {
          console.log('❌ 任务删除失败:', deleteResult.response?.error?.message || '未知错误');
        }
      }
    } else {
      console.log('❌ 任务可能没有被真正创建！数据库中任务数量没有变化');
    }
    
  } catch (error) {
    console.error('深度功能验证失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始XItools MCP工具验证...');
  
  // 1. 验证工具列表
  const tools = await verifyAllTools();
  
  if (tools.length === 0) {
    console.log('❌ 无法获取工具列表，退出验证');
    return;
  }
  
  // 2. 深度功能验证
  await deepFunctionalityTest();
  
  console.log('\n🎯 验证完成！');
}

// 运行验证
main().catch(error => {
  console.error('验证失败:', error);
  process.exit(1);
});
