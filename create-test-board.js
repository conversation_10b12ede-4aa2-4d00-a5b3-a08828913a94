/**
 * 创建测试看板的脚本
 * 
 * 此脚本将：
 * 1. 使用JWT token登录
 * 2. 获取默认工作区
 * 3. 创建一个测试看板
 * 4. 创建默认的看板列
 */

const http = require('http');

// 测试配置
const config = {
  baseUrl: 'http://localhost:3000',
  testUser: {
    username: 'mcp_test_user',
    email: '<EMAIL>',
    password: 'test_password_123'
  }
};

/**
 * 发送HTTP请求的通用函数
 */
async function sendRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const requestData = data ? JSON.stringify(data) : null;
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (requestData) {
      options.headers['Content-Length'] = Buffer.byteLength(requestData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: response
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (requestData) {
      req.write(requestData);
    }
    req.end();
  });
}

/**
 * 登录获取JWT token
 */
async function loginUser() {
  console.log('🔑 登录用户...');
  
  try {
    const loginData = {
      identifier: config.testUser.email,
      password: config.testUser.password
    };
    
    const result = await sendRequest('POST', '/api/auth/login', loginData);
    
    if (result.status === 200 && result.data.success) {
      console.log('✅ 用户登录成功');
      return result.data.data.token;
    } else {
      console.log('❌ 用户登录失败');
      console.log(`   状态码: ${result.status}`);
      console.log(`   错误: ${result.data.error || result.data}`);
      return null;
    }
  } catch (error) {
    console.error('登录用户时发生错误:', error.message);
    return null;
  }
}

/**
 * 获取用户工作区
 */
async function getUserWorkspaces(jwtToken) {
  console.log('🏢 获取用户工作区...');
  
  try {
    const result = await sendRequest('GET', '/api/workspaces', null, {
      'Authorization': `Bearer ${jwtToken}`
    });
    
    if (result.status === 200 && result.data.success) {
      const workspaces = result.data.data;
      console.log(`✅ 找到 ${workspaces.length} 个工作区`);
      
      if (workspaces.length > 0) {
        const defaultWorkspace = workspaces[0];
        console.log(`   默认工作区: ${defaultWorkspace.name} (${defaultWorkspace.id})`);
        return defaultWorkspace;
      }
    } else {
      console.log('❌ 获取工作区失败');
      console.log(`   状态码: ${result.status}`);
      console.log(`   错误: ${result.data.error || result.data}`);
    }
    
    return null;
  } catch (error) {
    console.error('获取工作区时发生错误:', error.message);
    return null;
  }
}

/**
 * 创建测试看板
 */
async function createTestBoard(jwtToken, workspaceId) {
  console.log('📋 创建测试看板...');
  
  try {
    const boardData = {
      name: 'MCP测试看板',
      description: '用于测试MCP工具的看板',
      workspaceId: workspaceId
    };
    
    const result = await sendRequest('POST', '/api/boards', boardData, {
      'Authorization': `Bearer ${jwtToken}`
    });
    
    if (result.status === 200 && result.data.success) {
      const board = result.data.data;
      console.log('✅ 测试看板创建成功');
      console.log(`   看板名称: ${board.name}`);
      console.log(`   看板ID: ${board.id}`);
      return board;
    } else {
      console.log('❌ 创建看板失败');
      console.log(`   状态码: ${result.status}`);
      console.log(`   错误: ${result.data.error || result.data}`);
      return null;
    }
  } catch (error) {
    console.error('创建看板时发生错误:', error.message);
    return null;
  }
}

/**
 * 创建默认看板列
 */
async function createDefaultColumns(jwtToken, boardId) {
  console.log('📊 创建默认看板列...');
  
  const defaultColumns = [
    { name: '待办', color: '#e3f2fd', position: 0 },
    { name: '进行中', color: '#fff3e0', position: 1 },
    { name: '已完成', color: '#e8f5e8', position: 2 }
  ];
  
  const createdColumns = [];
  
  for (const columnData of defaultColumns) {
    try {
      const result = await sendRequest('POST', `/api/boards/${boardId}/columns`, columnData, {
        'Authorization': `Bearer ${jwtToken}`
      });
      
      if (result.status === 200 && result.data.success) {
        const column = result.data.data;
        console.log(`✅ 创建列: ${column.name} (${column.id})`);
        createdColumns.push(column);
      } else {
        console.log(`❌ 创建列 "${columnData.name}" 失败`);
        console.log(`   状态码: ${result.status}`);
        console.log(`   错误: ${result.data.error || result.data}`);
      }
    } catch (error) {
      console.error(`创建列 "${columnData.name}" 时发生错误:`, error.message);
    }
  }
  
  return createdColumns;
}

/**
 * 主函数
 */
async function createTestEnvironment() {
  console.log('🚀 开始创建XItools MCP测试环境...');
  console.log(`📡 目标服务器: ${config.baseUrl}`);
  
  // 1. 登录用户
  const jwtToken = await loginUser();
  if (!jwtToken) {
    console.log('❌ 无法获取JWT token，退出');
    return;
  }
  
  // 2. 获取工作区
  const workspace = await getUserWorkspaces(jwtToken);
  if (!workspace) {
    console.log('❌ 无法获取工作区，退出');
    return;
  }
  
  // 3. 创建测试看板
  const board = await createTestBoard(jwtToken, workspace.id);
  if (!board) {
    console.log('❌ 无法创建测试看板，退出');
    return;
  }
  
  // 4. 创建默认列
  const columns = await createDefaultColumns(jwtToken, board.id);
  
  console.log('\n🎯 测试环境创建完成！');
  console.log('📋 环境信息摘要:');
  console.log(`   工作区: ${workspace.name} (${workspace.id})`);
  console.log(`   看板: ${board.name} (${board.id})`);
  console.log(`   列数量: ${columns.length}`);
  
  if (columns.length > 0) {
    console.log('   列信息:');
    columns.forEach((column, index) => {
      console.log(`     ${index + 1}. ${column.name} (${column.id})`);
    });
  }
  
  console.log('\n🔧 下一步:');
  console.log('现在可以运行 node test-all-mcp-tools.js 来测试所有MCP工具');
  
  return {
    workspace,
    board,
    columns
  };
}

// 运行创建
createTestEnvironment().catch(error => {
  console.error('创建测试环境失败:', error);
  process.exit(1);
});
