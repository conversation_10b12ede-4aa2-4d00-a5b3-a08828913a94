/**
 * XItools 完整MCP工具测试脚本
 * 
 * 测试所有16个MCP工具的功能
 */

const http = require('http');

const config = {
  baseUrl: 'http://localhost:3000',
  mcpEndpoint: '/mcp-auth',
  apiKey: 'xitool_71b0a8b4d0706d6b24bd5f26acb6cd7b794f45acce20dd0a9a73cff7a9e35527',
  timeout: 10000
};

const testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

async function sendJsonRpcRequest(method, params = {}) {
  return new Promise((resolve, reject) => {
    const requestData = JSON.stringify({
      jsonrpc: '2.0',
      method: method,
      params: params,
      id: Date.now()
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: config.mcpEndpoint,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(requestData),
        'Authorization': `Bearer ${config.apiKey}`
      },
      timeout: config.timeout
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => { data += chunk; });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ status: res.statusCode, response, data });
        } catch (error) {
          resolve({ status: res.statusCode, response: null, data, parseError: error.message });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => { req.destroy(); reject(new Error('请求超时')); });
    req.write(requestData);
    req.end();
  });
}

function logTest(testName, status, details = '') {
  testResults.total++;
  const statusIcon = status ? '✅' : '❌';
  console.log(`${statusIcon} ${testName}: ${status ? '通过' : '失败'}`);
  if (details) console.log(`   详情: ${details}`);
  
  if (status) {
    testResults.passed++;
  } else {
    testResults.failed++;
    testResults.errors.push(`${testName}: ${details}`);
  }
}

async function testToolCall(toolName, params = {}) {
  try {
    const result = await sendJsonRpcRequest('tools/call', {
      name: toolName,
      arguments: params
    });

    const success = result.status === 200 && result.response && !result.response.error;
    let details = '';
    
    if (success) {
      if (result.response.result && result.response.result.content) {
        details = `返回内容长度: ${JSON.stringify(result.response.result.content).length}`;
      } else {
        details = '调用成功';
      }
    } else {
      if (result.response && result.response.error) {
        details = `错误: ${result.response.error.message}`;
      } else {
        details = `状态码: ${result.status}`;
      }
    }
    
    logTest(`工具调用: ${toolName}`, success, details);
    return { success, result: result.response };
  } catch (error) {
    logTest(`工具调用: ${toolName}`, false, error.message);
    return { success: false, error: error.message };
  }
}

async function runCompleteTest() {
  console.log('🚀 开始XItools 16个MCP工具完整测试...');
  
  // 1. 获取工具列表
  const toolsResult = await sendJsonRpcRequest('tools/list');
  if (!toolsResult.response || !toolsResult.response.result) {
    console.log('❌ 无法获取工具列表');
    return;
  }
  
  const tools = toolsResult.response.result.tools;
  console.log(`✅ 发现 ${tools.length} 个工具\n`);
  
  // 2. 获取测试数据
  const boardsResult = await testToolCall('get_boards');
  if (!boardsResult.success) {
    console.log('❌ 无法获取看板信息');
    return;
  }
  
  const boards = JSON.parse(boardsResult.result.result.content[0].text);
  const testBoard = boards[0];
  console.log(`📋 使用测试看板: ${testBoard.name}\n`);
  
  // 3. 测试项目管理工具
  console.log('🏢 测试项目管理工具:');
  await testToolCall('get_workspaces');
  await testToolCall('get_projects');
  await testToolCall('get_boards');
  
  // 4. 测试看板列管理工具
  console.log('\n📊 测试看板列管理工具:');
  await testToolCall('get_columns', { boardId: testBoard.id });
  
  // 创建测试列
  const createColumnResult = await testToolCall('create_column', {
    column_data: {
      name: 'MCP测试列',
      boardId: testBoard.id,
      order: 999,
      color: '#ff9800',
      isDefault: false
    }
  });
  
  let testColumnId = null;
  if (createColumnResult.success) {
    try {
      const createdColumn = JSON.parse(createColumnResult.result.result.content[0].text);
      testColumnId = createdColumn.id;
      console.log(`   创建的测试列ID: ${testColumnId}`);
    } catch (e) {
      console.log('   无法解析创建的列信息');
    }
  }
  
  if (testColumnId) {
    // 测试列更新
    await testToolCall('update_column', {
      column_id: testColumnId,
      updates: {
        name: 'MCP测试列 (已更新)',
        color: '#4caf50'
      }
    });
    
    // 测试列重排序
    const columnsResult = await testToolCall('get_columns', { boardId: testBoard.id });
    if (columnsResult.success) {
      try {
        const columns = JSON.parse(columnsResult.result.result.content[0].text);
        const columnIds = columns.map(c => c.id);
        await testToolCall('reorder_columns', {
          boardId: testBoard.id,
          column_ids: columnIds.reverse()
        });
      } catch (e) {
        console.log('   无法测试列重排序');
      }
    }
  }
  
  // 5. 测试任务管理工具
  console.log('\n📝 测试任务管理工具:');
  await testToolCall('get_task_schema', { boardId: testBoard.id });
  await testToolCall('list_tasks');
  
  // 创建测试任务
  const createTaskResult = await testToolCall('submit_task_dataset', {
    tasks: [{
      title: '完整测试任务',
      description: '用于测试所有MCP工具功能的任务',
      priority: 'High',
      boardId: testBoard.id,
      status: 'todo'
    }]
  });
  
  let testTaskId = null;
  if (createTaskResult.success) {
    const listResult = await testToolCall('list_tasks');
    if (listResult.success) {
      try {
        const tasks = JSON.parse(listResult.result.result.content[0].text);
        const testTask = tasks.find(t => t.title.includes('完整测试任务'));
        if (testTask) {
          testTaskId = testTask.id;
          console.log(`   创建的测试任务ID: ${testTaskId}`);
        }
      } catch (e) {
        console.log('   无法解析任务列表');
      }
    }
  }
  
  if (testTaskId) {
    // 测试任务详情
    await testToolCall('get_task_details', { task_id: testTaskId });
    
    // 测试任务更新
    await testToolCall('update_task', {
      task_id: testTaskId,
      updates: {
        title: '完整测试任务 (已更新)',
        priority: 'Medium'
      }
    });
    
    // 测试任务颜色更新
    await testToolCall('update_task_color', {
      task_id: testTaskId,
      color: '#e91e63'
    });
    
    // 测试任务删除
    await testToolCall('delete_task', { task_id: testTaskId });
  }
  
  // 6. 测试实用工具
  console.log('\n🧹 测试实用工具:');
  await testToolCall('clear_all_tasks');
  
  // 7. 清理测试数据
  if (testColumnId) {
    console.log('\n🧹 清理测试数据:');
    await testToolCall('delete_column', { column_id: testColumnId });
  }
  
  // 8. 输出测试结果
  console.log('\n📊 测试结果摘要:');
  console.log(`   总测试数: ${testResults.total}`);
  console.log(`   通过: ${testResults.passed}`);
  console.log(`   失败: ${testResults.failed}`);
  console.log(`   成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }
  
  console.log('\n🎯 完整测试完成!');
}

runCompleteTest().catch(error => {
  console.error('测试失败:', error);
  process.exit(1);
});
