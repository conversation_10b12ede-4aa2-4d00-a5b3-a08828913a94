/**
 * 设置测试用户和API密钥的脚本
 * 
 * 此脚本将：
 * 1. 注册一个测试用户
 * 2. 登录获取JWT token
 * 3. 创建一个API密钥用于MCP测试
 */

const http = require('http');

// 测试配置
const config = {
  baseUrl: 'http://localhost:3000',
  testUser: {
    username: 'mcp_test_user',
    email: '<EMAIL>',
    password: 'test_password_123'
  }
};

/**
 * 发送HTTP请求的通用函数
 */
async function sendRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const requestData = data ? JSON.stringify(data) : null;
    
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (requestData) {
      options.headers['Content-Length'] = Buffer.byteLength(requestData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: response
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (requestData) {
      req.write(requestData);
    }
    req.end();
  });
}

/**
 * 注册测试用户
 */
async function registerTestUser() {
  console.log('🔧 注册测试用户...');
  
  try {
    const result = await sendRequest('POST', '/api/auth/register', config.testUser);
    
    if (result.status === 200 && result.data.success) {
      console.log('✅ 用户注册成功');
      console.log(`   用户名: ${config.testUser.username}`);
      console.log(`   邮箱: ${config.testUser.email}`);
      return result.data.data.token;
    } else {
      console.log('❌ 用户注册失败');
      console.log(`   状态码: ${result.status}`);
      console.log(`   错误: ${result.data.error || result.data}`);
      
      // 如果用户已存在，尝试登录
      if (result.data.error && result.data.error.includes('已存在')) {
        console.log('🔄 用户已存在，尝试登录...');
        return await loginTestUser();
      }
      
      return null;
    }
  } catch (error) {
    console.error('注册用户时发生错误:', error.message);
    return null;
  }
}

/**
 * 登录测试用户
 */
async function loginTestUser() {
  console.log('🔑 登录测试用户...');
  
  try {
    const loginData = {
      identifier: config.testUser.email,
      password: config.testUser.password
    };
    
    const result = await sendRequest('POST', '/api/auth/login', loginData);
    
    if (result.status === 200 && result.data.success) {
      console.log('✅ 用户登录成功');
      return result.data.data.token;
    } else {
      console.log('❌ 用户登录失败');
      console.log(`   状态码: ${result.status}`);
      console.log(`   错误: ${result.data.error || result.data}`);
      return null;
    }
  } catch (error) {
    console.error('登录用户时发生错误:', error.message);
    return null;
  }
}

/**
 * 创建API密钥
 */
async function createApiKey(jwtToken) {
  console.log('🔐 创建API密钥...');
  
  try {
    const apiKeyData = {
      name: 'MCP测试密钥',
      permissions: ['mcp:read', 'mcp:write'],
      expiresAt: null // 永不过期
    };
    
    const result = await sendRequest('POST', '/api/user/api-keys', apiKeyData, {
      'Authorization': `Bearer ${jwtToken}`
    });
    
    if (result.status === 200 && result.data.success) {
      console.log('✅ API密钥创建成功');
      console.log(`   密钥名称: ${result.data.data.name}`);
      console.log(`   密钥前缀: ${result.data.data.keyPrefix}`);
      console.log(`   权限: ${result.data.data.permissions.join(', ')}`);
      console.log(`   完整密钥: ${result.data.data.apiKey}`);
      return result.data.data.apiKey;
    } else {
      console.log('❌ API密钥创建失败');
      console.log(`   状态码: ${result.status}`);
      console.log(`   错误: ${result.data.error || result.data}`);
      return null;
    }
  } catch (error) {
    console.error('创建API密钥时发生错误:', error.message);
    return null;
  }
}

/**
 * 获取现有API密钥列表
 */
async function getExistingApiKeys(jwtToken) {
  console.log('📋 检查现有API密钥...');
  
  try {
    const result = await sendRequest('GET', '/api/user/api-keys', null, {
      'Authorization': `Bearer ${jwtToken}`
    });
    
    if (result.status === 200 && result.data.success) {
      const apiKeys = result.data.data;
      console.log(`✅ 找到 ${apiKeys.length} 个现有API密钥`);
      
      // 查找MCP测试密钥
      const mcpTestKey = apiKeys.find(key => key.name === 'MCP测试密钥' && key.isActive);
      if (mcpTestKey) {
        console.log('🔍 找到现有的MCP测试密钥');
        console.log(`   密钥前缀: ${mcpTestKey.keyPrefix}`);
        console.log('   注意: 完整密钥只在创建时显示，需要重新创建');
        return null; // 返回null表示需要创建新密钥
      }
      
      return null;
    } else {
      console.log('❌ 获取API密钥列表失败');
      return null;
    }
  } catch (error) {
    console.error('获取API密钥列表时发生错误:', error.message);
    return null;
  }
}

/**
 * 主函数
 */
async function setupTestUser() {
  console.log('🚀 开始设置XItools MCP测试环境...');
  console.log(`📡 目标服务器: ${config.baseUrl}`);
  
  // 1. 注册或登录用户
  let jwtToken = await registerTestUser();
  
  if (!jwtToken) {
    console.log('❌ 无法获取JWT token，退出');
    return;
  }
  
  console.log('✅ JWT token获取成功');
  console.log(`   Token前缀: ${jwtToken.substring(0, 20)}...`);
  
  // 2. 检查现有API密钥
  await getExistingApiKeys(jwtToken);
  
  // 3. 创建新的API密钥
  const apiKey = await createApiKey(jwtToken);
  
  if (!apiKey) {
    console.log('❌ 无法创建API密钥，退出');
    return;
  }
  
  console.log('\n🎯 设置完成！');
  console.log('📋 测试信息摘要:');
  console.log(`   用户名: ${config.testUser.username}`);
  console.log(`   邮箱: ${config.testUser.email}`);
  console.log(`   API密钥: ${apiKey}`);
  
  console.log('\n🔧 下一步:');
  console.log('1. 复制上面的API密钥');
  console.log('2. 更新 test-all-mcp-tools.js 中的 config.apiKey');
  console.log('3. 运行 node test-all-mcp-tools.js');
  
  return {
    jwtToken,
    apiKey,
    user: config.testUser
  };
}

// 运行设置
setupTestUser().catch(error => {
  console.error('设置失败:', error);
  process.exit(1);
});
