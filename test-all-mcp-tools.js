/**
 * XItools MCP工具全面测试脚本
 * 
 * 此脚本测试所有14个MCP工具的功能，包括：
 * - 任务管理工具 (6个)
 * - 看板列管理工具 (5个) 
 * - 实用工具 (3个)
 */

const https = require('https');
const http = require('http');

// 测试配置
const config = {
  baseUrl: 'http://localhost:3000',
  mcpEndpoint: '/mcp-auth',
  // 测试用的API密钥 - 从setup-test-user.js获取
  apiKey: 'xitool_71b0a8b4d0706d6b24bd5f26acb6cd7b794f45acce20dd0a9a73cff7a9e35527',
  timeout: 10000
};

// 测试结果统计
const testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

/**
 * 发送JSON-RPC请求到MCP端点
 */
async function sendJsonRpcRequest(method, params = {}) {
  return new Promise((resolve, reject) => {
    const requestData = JSON.stringify({
      jsonrpc: '2.0',
      method: method,
      params: params,
      id: Date.now()
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: config.mcpEndpoint,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(requestData),
        'Authorization': `Bearer ${config.apiKey}`
      },
      timeout: config.timeout
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({
            status: res.statusCode,
            contentType: res.headers['content-type'],
            response: response,
            data: data
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            contentType: res.headers['content-type'],
            response: null,
            data: data,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.write(requestData);
    req.end();
  });
}

/**
 * 测试工具函数
 */
function logTest(testName, status, details = '') {
  testResults.total++;
  const statusIcon = status ? '✅' : '❌';
  console.log(`${statusIcon} ${testName}: ${status ? '通过' : '失败'}`);
  
  if (details) {
    console.log(`   详情: ${details}`);
  }
  
  if (status) {
    testResults.passed++;
  } else {
    testResults.failed++;
    testResults.errors.push(`${testName}: ${details}`);
  }
}

/**
 * 测试MCP协议初始化
 */
async function testInitialize() {
  console.log('\n🔧 测试MCP协议初始化...');
  
  try {
    const result = await sendJsonRpcRequest('initialize', {
      protocolVersion: '2025-03-26',
      capabilities: {
        tools: {}
      },
      clientInfo: {
        name: 'xitools-test-client',
        version: '1.0.0'
      }
    });

    const success = result.status === 200 && 
                   result.response && 
                   result.response.result &&
                   result.response.result.serverInfo;
    
    logTest('MCP初始化', success, 
      success ? `服务器: ${result.response.result.serverInfo.name}` : 
                `状态码: ${result.status}, 响应: ${result.data}`);
    
    return success;
  } catch (error) {
    logTest('MCP初始化', false, error.message);
    return false;
  }
}

/**
 * 测试工具列表获取
 */
async function testToolsList() {
  console.log('\n📋 测试工具列表获取...');
  
  try {
    const result = await sendJsonRpcRequest('tools/list');
    
    const success = result.status === 200 && 
                   result.response && 
                   result.response.result &&
                   Array.isArray(result.response.result.tools);
    
    const toolCount = success ? result.response.result.tools.length : 0;
    
    logTest('获取工具列表', success, 
      success ? `发现 ${toolCount} 个工具` : 
                `状态码: ${result.status}, 响应: ${result.data}`);
    
    if (success && toolCount > 0) {
      console.log('   可用工具:');
      result.response.result.tools.forEach((tool, index) => {
        console.log(`   ${index + 1}. ${tool.name} - ${tool.description}`);
      });
    }
    
    return success ? result.response.result.tools : [];
  } catch (error) {
    logTest('获取工具列表', false, error.message);
    return [];
  }
}

/**
 * 测试单个工具调用
 */
async function testToolCall(toolName, params = {}) {
  try {
    const result = await sendJsonRpcRequest('tools/call', {
      name: toolName,
      arguments: params
    });

    const success = result.status === 200 && 
                   result.response && 
                   !result.response.error;
    
    let details = '';
    if (success) {
      if (result.response.result && result.response.result.content) {
        details = `返回内容长度: ${JSON.stringify(result.response.result.content).length}`;
      } else {
        details = '调用成功';
      }
    } else {
      if (result.response && result.response.error) {
        details = `错误: ${result.response.error.message}`;
      } else {
        details = `状态码: ${result.status}`;
      }
    }
    
    logTest(`工具调用: ${toolName}`, success, details);
    return { success, result: result.response };
  } catch (error) {
    logTest(`工具调用: ${toolName}`, false, error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 发送普通API请求（非MCP）
 */
async function sendApiRequest(method, path, data = null, headers = {}) {
  return new Promise((resolve, reject) => {
    const requestData = data ? JSON.stringify(data) : null;

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`,
        ...headers
      }
    };

    if (requestData) {
      options.headers['Content-Length'] = Buffer.byteLength(requestData);
    }

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(responseData);
          resolve({
            status: res.statusCode,
            data: response
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: responseData,
            parseError: error.message
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (requestData) {
      req.write(requestData);
    }
    req.end();
  });
}

/**
 * 设置测试数据（创建工作区、项目、看板）
 */
async function setupTestData() {
  console.log('\n🏗️ 设置测试数据...');

  try {
    // 1. 获取工作区列表
    console.log('📋 获取工作区列表...');
    const workspacesResult = await testToolCall('get_workspaces');

    if (!workspacesResult.success) {
      console.log('❌ 无法获取工作区列表');
      return null;
    }

    // 2. 获取项目列表
    console.log('📋 获取项目列表...');
    const projectsResult = await testToolCall('get_projects');

    // 3. 获取看板列表
    console.log('📋 获取看板列表...');
    const boardsResult = await testToolCall('get_boards');

    if (boardsResult.success && boardsResult.result && boardsResult.result.result) {
      const boards = JSON.parse(boardsResult.result.result.content[0].text);
      if (boards.length > 0) {
        console.log(`✅ 找到 ${boards.length} 个看板`);
        return {
          boardId: boards[0].id,
          boardName: boards[0].name
        };
      }
    }

    console.log('⚠️ 用户暂无看板，某些测试将跳过');
    return null;

  } catch (error) {
    console.error('设置测试数据时发生错误:', error.message);
    return null;
  }
}

/**
 * 主测试函数
 */
async function runAllTests() {
  console.log('🚀 开始XItools MCP工具全面测试...');
  console.log(`📡 测试端点: ${config.baseUrl}${config.mcpEndpoint}`);
  console.log(`🔑 使用API密钥: ${config.apiKey.substring(0, 20)}...`);

  // 1. 测试MCP协议初始化
  const initSuccess = await testInitialize();
  if (!initSuccess) {
    console.log('\n❌ MCP初始化失败，跳过后续测试');
    return;
  }

  // 2. 测试工具列表
  const tools = await testToolsList();
  if (tools.length === 0) {
    console.log('\n❌ 无法获取工具列表，跳过工具测试');
    return;
  }

  // 3. 设置测试数据
  const testData = await setupTestData();

  console.log('\n🔧 开始测试各个工具...');

  // 4. 测试项目管理工具
  console.log('\n🏢 测试项目管理工具:');
  await testToolCall('get_workspaces');
  await testToolCall('get_projects');
  await testToolCall('get_boards');

  // 5. 测试任务管理工具
  console.log('\n📝 测试任务管理工具:');
  await testToolCall('list_tasks');

  // 6. 测试需要boardId的工具
  if (testData && testData.boardId) {
    console.log(`\n📊 测试看板相关工具 (使用看板: ${testData.boardName}):`);
    await testToolCall('get_task_schema', { boardId: testData.boardId });
    await testToolCall('get_columns', { boardId: testData.boardId });

    // 测试任务创建
    console.log('\n➕ 测试任务创建:');
    const createTaskResult = await testToolCall('submit_task_dataset', {
      tasks: [{
        title: 'MCP测试任务',
        description: '这是通过MCP工具创建的测试任务',
        priority: 'Medium',
        boardId: testData.boardId,
        status: 'todo' // 使用默认状态
      }]
    });

    if (createTaskResult.success) {
      console.log('✅ 任务创建测试完成，现在测试其他任务操作...');

      // 重新列出任务查看创建结果
      const listResult = await testToolCall('list_tasks');

      // 尝试获取任务详情
      if (listResult.success && listResult.result && listResult.result.result) {
        try {
          const tasks = JSON.parse(listResult.result.result.content[0].text);
          if (tasks.length > 0) {
            const firstTask = tasks[0];
            console.log('\n🔍 测试任务详情获取:');
            await testToolCall('get_task_details', { task_id: firstTask.id });

            console.log('\n✏️ 测试任务更新:');
            await testToolCall('update_task', {
              task_id: firstTask.id,
              updates: {
                title: 'MCP测试任务 (已更新)',
                description: '这是通过MCP工具更新的测试任务',
                priority: 'High'
              }
            });

            console.log('\n🗑️ 测试任务删除:');
            await testToolCall('delete_task', { task_id: firstTask.id });
          }
        } catch (error) {
          console.log('⚠️ 解析任务列表失败，跳过详细测试');
        }
      }
    }
  } else {
    console.log('\n⚠️ 跳过需要看板ID的测试（用户暂无看板）');
  }

  // 7. 测试清理工具
  console.log('\n🧹 测试清理工具:');
  await testToolCall('clear_all_tasks');

  // 输出测试结果摘要
  console.log('\n📊 测试结果摘要:');
  console.log(`   总测试数: ${testResults.total}`);
  console.log(`   通过: ${testResults.passed}`);
  console.log(`   失败: ${testResults.failed}`);
  console.log(`   成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);

  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }

  console.log('\n🎯 测试完成!');
}

// 运行测试
runAllTests().catch(error => {
  console.error('测试运行失败:', error);
  process.exit(1);
});
